# DTO Enhancement Project Requirements Document (PRD)

## Project Overview

**Project Name:** DTO Implementation Enhancement  
**Version:** 2.0  
**Date:** 2025-01-27  
**Status:** Planning Phase  

### Executive Summary

This document outlines the requirements for enhancing the existing DTO implementation in the Agricultural Platform project. Building upon the solid foundation already established, this phase focuses on completing the mapper layer and introducing architectural improvements through MapStruct integration and advanced validation patterns.

## Current State Analysis

### Strengths
- ✅ Comprehensive DTO coverage for all domain entities
- ✅ Well-structured package organization by domain
- ✅ Security-focused design (password exclusion, data hiding)
- ✅ Consistent naming conventions (Summary/Detail/Create patterns)
- ✅ Jakarta Bean Validation integration

### Identified Gaps
- ❌ Missing mappers for Event, Package, Order, and Cultivation domains
- ❌ Manual mapper implementation (boilerplate code)
- ❌ Limited custom validation for business rules
- ❌ Inconsistent null-safety patterns across mappers

## Project Objectives

### Primary Goals
1. **Complete Mapper Coverage:** Implement missing mappers for all domain entities
2. **MapStruct Migration:** Transition from manual to annotation-based mapping
3. **Enhanced Validation:** Implement domain-specific business rule validation
4. **Code Quality:** Improve maintainability and reduce boilerplate

### Success Criteria
- [ ] All domain entities have corresponding mappers
- [ ] 100% MapStruct annotation coverage for new and migrated mappers
- [ ] Custom validators implemented for critical business rules
- [ ] Zero manual mapping boilerplate in new implementations
- [ ] Consistent null-safety across all mappers

## Technical Requirements

### Priority 2: Mapper Implementation

#### 2.1 EventoMapper
**Scope:** Handle complex relationships with organizers and participating companies

**Requirements:**
- Convert Evento entity ↔ EventoSummaryDTO/EventoDetailDTO/CreateEventoRequestDTO
- Handle AnimatoreDellaFiliera → UserPublicDTO conversion
- Manage List<Venditore> → List<UserPublicDTO> for participating companies
- Calculate available seats (capienzaMassima - postiAttualmentePrenotati)
- Null-safe conversion for all nested relationships

**Key Mappings:**
```java
@Mapping(target = "postiDisponibili", expression = "java(evento.getPostiDisponibili())")
@Mapping(target = "organizzatore", source = "organizzatore")
@Mapping(target = "aziendePartecipanti", source = "aziendePartecipanti")
```

#### 2.2 PacchettoMapper
**Scope:** Manage package elements and distributor relationships

**Requirements:**
- Convert Pacchetto entity ↔ PacchettoSummaryDTO/PacchettoDetailDTO/CreatePacchettoRequestDTO
- Handle DistributoreDiTipicita → UserPublicDTO conversion
- Manage List<Acquistabile> → List<ElementoPacchettoDTO> conversion
- Implement type detection for polymorphic Acquistabile elements
- Calculate package totals and element counts

**Key Mappings:**
```java
@Mapping(target = "distributore", source = "distributore")
@Mapping(target = "elementiInclusi", source = "elementiInclusi")
@Mapping(target = "numeroElementi", expression = "java(pacchetto.getElementiInclusi().size())")
```

#### 2.3 OrdineMapper
**Scope:** Handle order lines and state management

**Requirements:**
- Convert Ordine entity ↔ OrdineSummaryDTO/OrdineDetailDTO/CreateOrdineRequestDTO
- Handle Acquirente → UserPublicDTO conversion
- Manage List<RigaOrdine> → List<RigaOrdineDTO> conversion
- Convert StatoCorrente enum properly
- Calculate order totals and line counts

**Key Mappings:**
```java
@Mapping(target = "acquirente", source = "acquirente")
@Mapping(target = "righeOrdine", source = "righeOrdine")
@Mapping(target = "numeroRighe", expression = "java(ordine.getRigheOrdine().size())")
```

#### 2.4 MetodoDiColtivazioneMapper
**Scope:** Complete cultivation domain coverage

**Requirements:**
- Convert MetodoDiColtivazione entity ↔ MetodoDiColtivazioneDTO
- Handle FonteMateriaPrima enum conversion (preserve type information)
- Manage cultivation technique and organic certification mappings
- Null-safe conversion for all fields

### Priority 3: MapStruct Integration

#### 3.1 Dependency Configuration
**Status:** ✅ Already configured in pom.xml

Verify the following dependencies are present:
```xml
<dependency>
    <groupId>org.mapstruct</groupId>
    <artifactId>mapstruct</artifactId>
    <version>1.5.5.Final</version>
</dependency>
<dependency>
    <groupId>org.mapstruct</groupId>
    <artifactId>mapstruct-processor</artifactId>
    <version>1.5.5.Final</version>
    <scope>provided</scope>
</dependency>
```

#### 3.2 Existing Mapper Migration

**UtenteMapper Migration:**
- Convert static methods to @Mapper interface
- Implement @Mapping annotations for field mappings
- Handle TipoRuolo enum conversion
- Maintain security exclusions (passwordHash)

**ProdottoMapper Migration:**
- Implement nested object mapping with @Mapping
- Handle TipoOrigineProdotto and StatoVerificaValori enums
- Manage Venditore → UserPublicDTO conversion
- Handle List<Certificazione> → List<CertificazioneDTO>

**CertificazioneMapper Migration:**
- Simple entity ↔ DTO conversion
- Date field handling
- Null-safe ID mappings

**CarrelloMapper Migration:**
- Complex polymorphic mapping for ElementoCarrello
- Type detection for Acquistabile implementations
- Calculated field mappings (totals, counts)

#### 3.3 MapStruct Configuration

**Base Mapper Configuration:**
```java
@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
```

**Shared Mapping Methods:**
- Implement @Named methods for common conversions
- Create utility methods for enum mappings
- Handle date/time conversions consistently

### Priority 3: Advanced Validation

#### 3.1 Custom Validators

**@ValidDateRange Validator:**
```java
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DateRangeValidator.class)
public @interface ValidDateRange {
    String start();
    String end();
    String message() default "Data di fine deve essere successiva alla data di inizio";
}
```

**@ValidCapacity Validator:**
```java
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CapacityValidator.class)
public @interface ValidCapacity {
    String capacity();
    String booked();
    String message() default "Posti prenotati non possono superare la capienza massima";
}
```

**@ValidBusinessHours Validator:**
```java
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = BusinessHoursValidator.class)
public @interface ValidBusinessHours {
    String message() default "L'orario deve essere durante le ore lavorative";
}
```

#### 3.2 Enhanced DTO Validation

**CreateEventoRequestDTO Enhancements:**
```java
@ValidDateRange(start = "dataOraInizio", end = "dataOraFine")
@ValidCapacity(capacity = "capienzaMassima", booked = "0")
public class CreateEventoRequestDTO {
    
    @NotNull(message = "La data di inizio è obbligatoria")
    @Future(message = "L'evento deve essere programmato nel futuro")
    @ValidBusinessHours
    private Date dataOraInizio;
    
    @NotNull(message = "La data di fine è obbligatoria")
    @ValidBusinessHours
    private Date dataOraFine;
    
    @Min(value = 1, message = "La capienza deve essere almeno 1")
    @Max(value = 1000, message = "La capienza non può superare 1000 persone")
    private int capienzaMassima;
}
```

**CreatePacchettoRequestDTO Enhancements:**
```java
public class CreatePacchettoRequestDTO {
    
    @NotEmpty(message = "Il pacchetto deve contenere almeno un elemento")
    @Size(max = 20, message = "Un pacchetto non può contenere più di 20 elementi")
    @Valid
    private List<ElementoPacchettoRequestDTO> elementiInclusi;
    
    @DecimalMin(value = "0.01", message = "Il prezzo deve essere maggiore di zero")
    @Digits(integer = 8, fraction = 2, message = "Formato prezzo non valido")
    private BigDecimal prezzoPacchetto;
}
```

## Implementation Plan

### Phase 1: Mapper Implementation 
1. **Day 1-2:** Implement EventoMapper with MapStruct
2. **Day 3-4:** Implement PacchettoMapper with MapStruct  
3. **Day 5-6:** Implement OrdineMapper with MapStruct
4. **Day 7-8:** Implement MetodoDiColtivazioneMapper with MapStruct
5. **Day 9-10:** Unit testing for all new mappers

### Phase 2: Existing Mapper Migration 
1. **Day 1-2:** Migrate UtenteMapper to MapStruct
2. **Day 3-4:** Migrate ProdottoMapper to MapStruct
3. **Day 5-6:** Migrate CertificazioneMapper and CarrelloMapper to MapStruct
4. **Day 7:** Integration testing and validation

### Phase 3: Advanced Validation 
1. **Day 1-2:** Implement custom validators
2. **Day 3-4:** Enhance DTO validation annotations
3. **Day 5-6:** Testing and validation message localization
4. **Day 7:** Documentation and code review

## Quality Assurance

### Testing Requirements
- [ ] Unit tests for all mapper conversions
- [ ] Null-safety testing for all mappers
- [ ] Validation testing for custom validators
- [ ] Integration tests for complex mappings
- [ ] Performance testing for large object graphs

### Code Quality Standards
- [ ] SonarQube quality gate compliance
- [ ] 90%+ test coverage for new mappers
- [ ] Zero critical/major code smells
- [ ] Consistent code formatting and documentation

## Risk Assessment

### Technical Risks
- **MapStruct Learning Curve:** Mitigated by comprehensive documentation and examples
- **Complex Relationship Mapping:** Addressed through incremental implementation and testing
- **Performance Impact:** Monitored through benchmarking and profiling

### Mitigation Strategies
- Implement mappers incrementally with thorough testing
- Maintain backward compatibility during migration
- Create comprehensive documentation for future maintenance

## Success Metrics

### Quantitative Metrics
- 100% mapper coverage for all domain entities
- 0 manual mapping boilerplate in new implementations
- <50ms average mapping time for complex objects
- 90%+ test coverage for mapper layer

### Qualitative Metrics
- Improved code maintainability and readability
- Reduced development time for future DTO additions
- Enhanced type safety and compile-time validation
- Better separation of concerns in the mapping layer

## Conclusion

This enhancement project will complete the DTO implementation foundation, providing a robust, maintainable, and performant mapping layer that supports the agricultural platform's API requirements. The focus on MapStruct integration and advanced validation will ensure the codebase remains scalable and maintainable as the platform evolves.
