HELP.md
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

# File compilati Java
*.class

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/
.DS_Store
docs/sequence-diagram-aggiornamento-quantita-prodotto.puml
docs/
example/
EsempioMiglioramento.java
/tests/
*.log
temp/
.env
CLAUDE.md
PRODOTTO_OBSERVER_USAGE.md
test/

# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.roo
.taskmaster
# OS specific
.roomodes
.claude/
.agent.md
.agent.local.md
.agent.local.md.new
DTO.md
DTO_IMPLEMENTATION.md
DTObetter.md
