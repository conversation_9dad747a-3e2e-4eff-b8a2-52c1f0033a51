# DTO Implementation Guide

## Overview

This document outlines the comprehensive DTO (Data Transfer Object) implementation for the Agricultural Platform project. The implementation follows the strategic plan outlined in `DTO.md` and provides secure, performant, and maintainable API endpoints.

## Architecture

### Package Structure

```
src/main/java/it/unicam/cs/ids/piattaforma_agricola_locale/
├── dto/
│   ├── utente/           # User-related DTOs
│   ├── catalogo/         # Product and Package DTOs
│   ├── carrello/         # Shopping cart DTOs
│   ├── ordine/           # Order DTOs
│   ├── eventi/           # Event DTOs
│   ├── coltivazione/     # Cultivation method DTOs
│   └── processo/         # Existing transformation process DTOs
├── service/mapper/       # DTO conversion utilities
└── controller/           # REST controllers using DTOs
```

## Implemented DTOs

### 1. User DTOs (`dto.utente`)

- **`UserDetailDTO`**: Complete user profile information (for own profile view)
- **`UserPublicDTO`**: Public user information (for displaying other users)
- **`UserUpdateDTO`**: User profile update requests

**Security Features:**
- `UserDetailDTO` excludes password hash but includes email for profile management
- `UserPublicDTO` excludes sensitive information like email and phone number
- Input validation with Jakarta Bean Validation annotations

### 2. Product DTOs (`dto.catalogo`)

- **`ProductSummaryDTO`**: Essential product info for catalog listings
- **`ProductDetailDTO`**: Complete product information with certifications and vendor
- **`CreateProductRequestDTO`**: Product creation with validation
- **`CertificazioneDTO`**: Product/company certification information

**Performance Features:**
- Summary DTOs minimize data transfer for list views
- Detail DTOs include nested objects (vendor, certifications) for single views
- Validation prevents invalid data entry

### 3. Cart DTOs (`dto.carrello`)

- **`CarrelloDTO`**: Complete cart with items and buyer info
- **`ElementoCarrelloDTO`**: Individual cart items with purchasable info
- **`AddToCartRequestDTO`**: Adding items to cart requests

**Flexibility Features:**
- Generic handling of different purchasable types (products, packages, events)
- Type-safe item identification with `tipoAcquistabile` field
- Calculated totals and item counts

### 4. Order DTOs (`dto.ordine`)

- **`OrdineSummaryDTO`**: Order overview for order history
- **`OrdineDetailDTO`**: Complete order with line items
- **`RigaOrdineDTO`**: Individual order line items
- **`CreateOrdineRequestDTO`**: Order creation from cart

**Business Logic Features:**
- State management with `StatoCorrente` enum
- Payment method specification
- Line-by-line order details

### 5. Package DTOs (`dto.catalogo`)

- **`PacchettoSummaryDTO`**: Package overview for listings
- **`PacchettoDetailDTO`**: Complete package with included items
- **`CreatePacchettoRequestDTO`**: Package creation with items

### 6. Event DTOs (`dto.eventi`)

- **`EventoSummaryDTO`**: Event overview for event listings
- **`EventoDetailDTO`**: Complete event with organizer and participants
- **`CreateEventoRequestDTO`**: Event creation with validation

**Event-Specific Features:**
- Capacity management (available vs. booked seats)
- Date validation (end after start, future dates)
- Participating company management

### 7. Cultivation DTOs (`dto.coltivazione`)

- **`MetodoDiColtivazioneDTO`**: Cultivation method information

## Mapper Utilities

### Core Mappers (`service.mapper`)

- **`UtenteMapper`**: User entity ↔ DTO conversions
- **`ProdottoMapper`**: Product entity ↔ DTO conversions
- **`CertificazioneMapper`**: Certification entity ↔ DTO conversions
- **`CarrelloMapper`**: Cart entity ↔ DTO conversions

**Mapper Features:**
- Null-safe conversions
- Type detection for polymorphic entities (Acquistabile)
- Nested object mapping (e.g., product with vendor and certifications)
- Separation of entity creation vs. update operations

## Controller Implementation

### Example: `ProdottoController`

The `ProdottoController` demonstrates best practices for DTO usage:

1. **Input Validation**: `@Valid` annotation on request DTOs
2. **Security**: No direct entity exposure, user authentication via headers
3. **Performance**: Different DTOs for list vs. detail views
4. **Error Handling**: Proper HTTP status codes and exception handling
5. **Pagination**: Support for paginated responses

**API Endpoints:**
- `GET /api/prodotti` → Returns `Page<ProductSummaryDTO>`
- `GET /api/prodotti/{id}` → Returns `ProductDetailDTO`
- `POST /api/prodotti` → Accepts `CreateProductRequestDTO`, returns `ProductDetailDTO`
- `PUT /api/prodotti/{id}` → Accepts `CreateProductRequestDTO`, returns `ProductDetailDTO`
- `DELETE /api/prodotti/{id}` → Returns void
- `GET /api/prodotti/search` → Returns `List<ProductSummaryDTO>`

## Key Benefits Achieved

### 1. Security
- **Password Protection**: Password hashes never appear in API responses
- **Data Hiding**: Internal implementation details are hidden from clients
- **Input Validation**: Jakarta Bean Validation prevents malformed requests
- **Mass Assignment Protection**: Only specific fields can be updated via DTOs

### 2. Performance
- **Optimized Payloads**: Summary DTOs reduce bandwidth for listings
- **Lazy Loading Safety**: DTOs prevent LazyInitializationException
- **Calculated Fields**: DTOs include computed values (totals, counts) without database calls

### 3. Maintainability
- **API Stability**: Entity changes don't break client contracts
- **Version Control**: Different DTO versions can coexist
- **Clear Separation**: Business logic stays in services, DTOs handle data transfer
- **Type Safety**: Compile-time checking for API contracts

### 4. Developer Experience
- **Clear Contracts**: DTOs serve as API documentation
- **IDE Support**: Strong typing enables better code completion
- **Validation Feedback**: Clear error messages for invalid inputs
- **Consistent Patterns**: Standardized approach across all endpoints

## Usage Guidelines

### When to Create DTOs
- **Always** for API endpoints that expose or consume data
- **Multiple DTOs per entity** for different use cases (summary vs. detail)
- **Request DTOs** for operations with specific validation needs
- **Response DTOs** to control exactly what data is exposed

### Naming Conventions
- `{Entity}SummaryDTO`: For list/overview displays
- `{Entity}DetailDTO`: For single item detailed views
- `Create{Entity}RequestDTO`: For creation operations
- `Update{Entity}RequestDTO`: For update operations (if different from create)

### Validation Strategy
- Use Jakarta Bean Validation annotations on DTO fields
- Implement custom validation for business rules
- Validate in controllers with `@Valid` annotation
- Return meaningful error messages to clients

## Migration Strategy

1. **Phase 1** ✅: Core DTOs (User, Product, Cart, Order)
2. **Phase 2** ✅: Extended DTOs (Package, Event, Certification)
3. **Phase 3** ✅: Mapper utilities and sample controller
4. **Phase 4**: Migrate remaining controllers to use DTOs
5. **Phase 5**: Optional MapStruct integration for reduced boilerplate

## Future Enhancements

### MapStruct Integration
Consider adding MapStruct for automatic mapping generation:

```xml
<dependency>
    <groupId>org.mapstruct</groupId>
    <artifactId>mapstruct</artifactId>
    <version>1.5.5.Final</version>
</dependency>
```

This would reduce manual mapper code and improve performance through compile-time code generation.

### API Versioning
As the API evolves, consider implementing versioning strategies:
- Path-based: `/api/v1/prodotti`, `/api/v2/prodotti`
- Header-based: `Accept: application/vnd.api+json;version=1`
- Parameter-based: `/api/prodotti?version=1`

## Conclusion

This DTO implementation provides a solid foundation for secure, performant, and maintainable APIs. The pattern can be extended to all remaining entities following the established conventions and guidelines.